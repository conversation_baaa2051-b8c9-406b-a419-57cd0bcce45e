const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const util = require('util');
const { app } = require('electron');
const { getAudioDuration, getEncoder,getVideoInfo } = require('../ffmpegHandler');
const { cssToASSColor, generateASSSubtitle, clampAtTempo } = require('./assBuild');
const {addAudioToVideo, upVolume } = require('./addAudioToVideo');
const { ffmpegManager } = require('../ffmpeg-config');
const execPromise = util.promisify(exec);

// Function to validate custom font name for ASS subtitle
function validateFontName(fontFamily) {
  if (!fontFamily || fontFamily === 'Arial' || fontFamily === 'Helvetica') {
    return fontFamily; // System fonts are always valid
  }

  try {
    // Get fonts directory
    const fontsDir = app.isPackaged
      ? path.join(process.resourcesPath, 'app.asar.unpacked', 'static', 'fonts')
      : path.join(app.getAppPath(), 'static', 'fonts');

    // Check if font file exists
    const fontFiles = fs.readdirSync(fontsDir).filter(file => {
      const name = path.basename(file, path.extname(file))
        .replace(/[-_]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
      return name === fontFamily;
    });

    if (fontFiles.length === 0) {
      console.log(`Font ${fontFamily} not found, using Arial`);
      return 'Arial';
    }

    console.log(`Font ${fontFamily} validated successfully`);
    return fontFamily;
  } catch (error) {
    console.error(`Error validating font ${fontFamily}:`, error);
    return 'Arial'; // Fallback to Arial
  }
}

const BATCH_SIZE = 20; // Giảm batch size để tránh memory overflow với nhiều audio inputs

// Helper function để xử lý batch với retry mechanism
async function processBatchWithRetry(event, batch, batchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime) {
  const maxRetries = 2;
  const retrySizes = [Math.ceil(batch.length / 2), Math.ceil(batch.length / 3)]; // Chia nhỏ batch khi retry

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt === 0) {
        // Lần đầu xử lý batch bình thường
        return await processSingleBatch(event, batch, batchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime);
      } else {
        // Retry với batch size nhỏ hơn
        const smallerBatchSize = retrySizes[attempt - 1];
        console.log(`🔄 Retry ${attempt}: Splitting batch ${batchIndex + 1} into smaller chunks of ${smallerBatchSize} items`);

        const subBatches = [];
        for (let i = 0; i < batch.length; i += smallerBatchSize) {
          subBatches.push(batch.slice(i, i + smallerBatchSize));
        }

        const subBatchPaths = [];
        for (let subIndex = 0; subIndex < subBatches.length; subIndex++) {
          const subBatch = subBatches[subIndex];
          const subBatchIndex = `${batchIndex}_${subIndex}`;
          const subBatchPath = await processSingleBatch(event, subBatch, subBatchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime);
          subBatchPaths.push(subBatchPath);
        }

        // Concatenate sub-batches
        if (subBatchPaths.length > 1) {
          const concatListPath = path.join(outputDir, `concat_batch_${batchIndex}.txt`);
          const concatListContent = subBatchPaths.map((p) => `file '${p}'`).join('\n');
          fs.writeFileSync(concatListPath, concatListContent);

          const finalBatchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);
          const encoder = await getEncoder();
          const concatCmd = `${ffmpegManager.ffmpegPath} -f concat -safe 0 -i "${concatListPath}" -c:v ${encoder} -preset fast -crf 23 -c:a aac -b:a 192k -y "${finalBatchOutput}"`;

          await execPromise(concatCmd);

          // Cleanup sub-batches
          subBatchPaths.forEach(p => {
            if (fs.existsSync(p)) fs.unlinkSync(p);
          });
          if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);

          return finalBatchOutput;
        } else {
          return subBatchPaths[0];
        }
      }
    } catch (error) {
      const isMemoryError = error.message.includes('Cannot allocate memory') ||
                           error.message.includes('out of memory') ||
                           error.message.includes('memory allocation');

      if (isMemoryError && attempt < maxRetries) {
        console.log(`⚠️ Memory error in batch ${batchIndex + 1}, attempt ${attempt + 1}. Retrying with smaller batch size...`);
        event?.sender?.send('video-task', {
          data: `⚠️ Memory error in batch ${batchIndex + 1}, retrying with smaller chunks...`,
          code: 0,
        });
        continue;
      } else {
        console.error(`❌ Batch ${batchIndex + 1} failed after ${attempt + 1} attempts:`, error.message);
        throw error;
      }
    }
  }
}

const resolutionMap = {
  '144p': 144,
  '240p': 240,
  '360p': 360,
  '480p': 480,
  '720p': 720,
  '1080p': 1080,
  '1440p': 1440,
  '2160p': 2160,
  '4k': 2160,
};

function getResolution(options) {
  const quality = options?.output?.quality || 'custom';
  const resolution = options?.videoInfo
  const defaultResolution = {width: resolution.width, height: resolution.height}
  if (quality === 'custom') return defaultResolution
  if (!quality) return defaultResolution

  const [qualityRaw, aspectRaw = '16:9'] = quality.toLowerCase().split('/');
  const qualityKey = /^\d+$/.test(qualityRaw) ? qualityRaw + 'p' : qualityRaw;

  const size = resolutionMap[qualityKey];
  if (!size) return null;

  const [wRatio, hRatio] = aspectRaw.split(':').map(Number);
  if (!wRatio || !hRatio) return null;

  let width, height;

  if (wRatio > hRatio) {
    // Landscape → height = size
    height = size;
    width = Math.round((height * wRatio) / hRatio);
  } else {
    // Portrait → width = size
    width = size;
    height = Math.round((width * hRatio) / wRatio);
  }

  return { width, height };
}


function buildBatchAndSrt(event, srtArray, BATCH_SIZE) {
  let adjustedCurrentTime = 0; // Thời gian tích luỹ sau khi điều chỉnh tốc độ

  for (let i = 0; i < srtArray.length; i++) {
    const currentSrt = srtArray[i];
    const nextSrt = srtArray[i + 1];

    // Thời lượng video segment gốc
    const originalVideoSegmentDuration = nextSrt
      ? nextSrt.startTime - currentSrt.startTime
      : currentSrt.endTime - currentSrt.startTime;

    // Ensure originalVideoSegmentDuration is valid and positive
    if (isNaN(originalVideoSegmentDuration) || originalVideoSegmentDuration <= 0) {
      console.warn(`⚠️ Invalid originalVideoSegmentDuration for segment ${i}: ${originalVideoSegmentDuration}, setting to 0.1s`);
      currentSrt.originalVideoSegmentDuration = 0.1;
    } else {
      currentSrt.originalVideoSegmentDuration = originalVideoSegmentDuration;
    }

    // Tính speed ratio using the corrected originalVideoSegmentDuration
    if (currentSrt.duration > currentSrt.originalVideoSegmentDuration) {
      currentSrt.speedRatio = currentSrt.originalVideoSegmentDuration / currentSrt.duration; // < 1 = chậm lại
    } else {
      currentSrt.speedRatio = 1; // không thay đổi tốc độ
    }

    // Thời lượng thực tế sau khi điều chỉnh tốc độ
    const adjustedVideoDuration = currentSrt.originalVideoSegmentDuration / currentSrt.speedRatio;

    // Thời lượng cuối cùng của đoạn này (lấy max giữa video đã điều chỉnh và audio)
    // Handle edge cases where durations might be 0 or invalid
    const validAdjustedVideoDuration = isNaN(adjustedVideoDuration) || adjustedVideoDuration <= 0 ? currentSrt.duration : adjustedVideoDuration;
    const validAudioDuration = isNaN(currentSrt.duration) || currentSrt.duration <= 0 ? 0.1 : currentSrt.duration; // Minimum 0.1s

    currentSrt.finalDuration = Math.max(validAdjustedVideoDuration, validAudioDuration);

    // Ensure finalDuration is never NaN or 0
    if (isNaN(currentSrt.finalDuration) || currentSrt.finalDuration <= 0) {
      console.warn(`⚠️ Invalid finalDuration for segment ${i}, setting to 0.1s`);
      currentSrt.finalDuration = 0.1;
    }

    // ====== REVERT: Giữ nguyên logic cũ cho subtitle timing ======
    // Cập nhật thời gian start/end mới cho SRT (không tính initial offset ở đây)
    currentSrt.adjustedStartTime = adjustedCurrentTime;
    currentSrt.adjustedEndTime = adjustedCurrentTime + currentSrt.finalDuration;

    // Cập nhật thời gian tích luỹ
    adjustedCurrentTime += currentSrt.finalDuration;
  }

  const batches = [];
  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    batches.push(srtArray.slice(i, i + BATCH_SIZE));
  }
  return batches;
}

// Tách phần xử lý batch thành hàm riêng để có thể retry
async function processSingleBatch(event, batch, batchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime) {
  const type = 'video-task';
  const holdOriginalAudio = options?.audio?.holdOriginalAudio || options?.audio?.holdMusicOnly || false;
  const addVoiceAudio = true;

  const inputs = [`-i "${videoPath}"`];
  let segmentFilters = [];

  // ====== FIX: currentTime should always start from 0 for each batch ======
  let currentTime = 0;

  let videoSegments = [];
  let originalAudioSegments = [];
  let voiceAudioSegments = [];

  // Add audio files as inputs and track mapping
  const audioInputMapping = new Map(); // Map SRT index to audio input index
  if (addVoiceAudio) {
    batch.forEach((srt, index) => {
      if (srt.audioUrl) {
        const audioPath = srt.audioUrl.replace('file://', '');
        if (fs.existsSync(audioPath)) {
          inputs.push(`-i "${audioPath}"`);
          audioInputMapping.set(index, inputs.length - 1); // Store the actual input index
          console.log(`🎵 SRT ${index} -> Audio Input ${inputs.length - 1}`);
        } else {
          console.warn(`⚠️ Audio file not found for SRT ${index}: ${audioPath}`);
        }
      } else {
        console.warn(`⚠️ No audioUrl for SRT ${index}`);
      }
    });
  }

  console.log(`🎵 Total audio inputs: ${inputs.length - 1}, Audio mapping:`, Array.from(audioInputMapping.entries()));

  // ====== FIX: Add initial video segment if first SRT doesn't start at 0 ======
  const firstSrt = batch[0];
  const isFirstBatch = batchIndex === 0;

  if (isFirstBatch && firstSrt && firstSrt.startTime > 0.1) {
    const initialDuration = firstSrt.startTime;
    console.log(`📺 Adding initial video segment: 0s to ${firstSrt.startTime.toFixed(2)}s (${initialDuration.toFixed(2)}s)`);

    // Add initial video segment
    let initialVideoFilter = `[0:v]trim=start=0:duration=${initialDuration.toFixed(6)},setpts=PTS-STARTPTS`;
    if (options.output?.quality) {
      const resolution = getResolution(options);
      initialVideoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    initialVideoFilter += `[v_initial]`;
    segmentFilters.push(initialVideoFilter);
    videoSegments.push(`[v_initial]`);

    // Add initial audio if needed
    if (holdOriginalAudio) {
      let initialAudioFilter = `[0:a]atrim=start=0:duration=${initialDuration.toFixed(6)},asetpts=PTS-STARTPTS`;
      initialAudioFilter += `,highpass=f=80,lowpass=f=8000`;
      initialAudioFilter += `[oa_initial]`;
      segmentFilters.push(initialAudioFilter);
      originalAudioSegments.push(`[oa_initial]`);
    }

    if (addVoiceAudio) {
      // Generate silence for initial duration
      let silentAudioFilter = `aevalsrc=0:duration=${initialDuration.toFixed(6)}:sample_rate=44100:channel_layout=stereo[va_initial]`;
      segmentFilters.push(silentAudioFilter);
      voiceAudioSegments.push(`[va_initial]`);
    }

    currentTime += initialDuration;
  }

  batch.forEach((srt, index) => {
    const vStart = srt.startTime;
    const vDur = srt.originalVideoSegmentDuration;
    const speedRatio = srt.speedRatio || 1;
    const finalDur = srt.finalDuration;

    // === VIDEO PROCESSING ===
    // Cắt video từ thời điểm startTime với duration chính xác
    let videoFilter = `[0:v]trim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},setpts=PTS-STARTPTS`;
    if (speedRatio < 1) {
      videoFilter += `,setpts=PTS/${speedRatio.toFixed(6)}`;
    }
    if (options.output?.quality) {
      const resolution = getResolution(options);
      options.output.resolution = resolution;
      videoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    videoFilter += `[v${index}]`;
    segmentFilters.push(videoFilter);
    videoSegments.push(`[v${index}]`);

    // === ORIGINAL AUDIO PROCESSING ===
    if (holdOriginalAudio) {
      // Cắt audio từ thời điểm startTime với duration chính xác
      let originalAudioFilter = `[0:a]atrim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},asetpts=PTS-STARTPTS`;

      // Improved tempo adjustment with smoother transitions
      if (speedRatio < 1) {
        let currentRatio = speedRatio;
        let tempoFilters = [];

        // Break down large tempo changes into smaller steps for better quality
        while (currentRatio < 0.5) {
          tempoFilters.push('atempo=0.5');
          currentRatio /= 0.5;
        }
        while (currentRatio > 2.0) {
          tempoFilters.push('atempo=2.0');
          currentRatio /= 2.0;
        }
        if (Math.abs(currentRatio - 1.0) > 0.001) {
          tempoFilters.push(`atempo=${currentRatio.toFixed(6)}`);
        }

        if (tempoFilters.length > 0) {
          originalAudioFilter += `,${tempoFilters.join(',')}`;
        }
      }

      // Add high-quality resampling and noise reduction
      // originalAudioFilter += `,aresample=44100:resampler=soxr:precision=28:cheby=1`;
      originalAudioFilter += `,highpass=f=80,lowpass=f=8000`; // Remove extreme frequencies
      originalAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[oa${index}]`;

      segmentFilters.push(originalAudioFilter);
      originalAudioSegments.push(`[oa${index}]`);
    }

    // === IMPROVED VOICE AUDIO PROCESSING ===
    if (addVoiceAudio && audioInputMapping.has(index)) {
      const voiceAudioIndex = audioInputMapping.get(index); // Use correct mapped input index
      console.log(`🎵 Processing SRT ${index} with audio input ${voiceAudioIndex}`);

      // No volume adjustment needed - loudnorm will handle consistent levels

      // Enhanced voice processing chain
      let voiceAudioFilter = `[${voiceAudioIndex}:a]`;

      // Aggressive normalization for consistent levels
      console.log(`🎵 SRT ${index}: Aggressive normalization for consistent levels`);
      voiceAudioFilter += `acompressor=threshold=-18dB:ratio=4:attack=3:release=30,`; // Strong compression first
      voiceAudioFilter += `loudnorm=I=-16:TP=-1.5:LRA=7,`; // Tighter loudness range
      voiceAudioFilter += `dynaudnorm=f=500:g=20:p=0.9`; // More aggressive dynamic normalization

      // Simple duration adjustment - preserve original voice
      const durationDiff = Math.abs(srt.duration - finalDur);
      if (srt.duration > finalDur && durationDiff > 0.01) {
        voiceAudioFilter += `,atrim=duration=${finalDur.toFixed(6)}`;
      } else if (srt.duration < finalDur && durationDiff > 0.01) {
        const padDuration = finalDur - srt.duration;
        voiceAudioFilter += `,apad=pad_dur=${padDuration.toFixed(6)}`;
      }

      voiceAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[va${index}]`;
      segmentFilters.push(voiceAudioFilter);
      voiceAudioSegments.push(`[va${index}]`);
    } else if (addVoiceAudio) {
      console.warn(`⚠️ Skipping voice audio for SRT ${index} - no audio mapping`);
    }

    currentTime += finalDur;
  });

  // ====== FIX: Only add remaining video in the LAST batch ======
  const isLastBatch = batchIndex === totalBatches - 1;

  // Calculate the actual end time of this batch
  let batchEndTime = 0;
  if (batch.length > 0) {
    const lastSrtInBatch = batch[batch.length - 1];
    batchEndTime = lastSrtInBatch.startTime + lastSrtInBatch.originalVideoSegmentDuration;
  }

  if (isLastBatch && batchEndTime < totalVideoDuration) {
    const remainingStart = batchEndTime; // Use batch end time instead of totalProcessedTime
    const remainingDuration = totalVideoDuration - batchEndTime;

    // ====== FIX: Only add remaining segment if duration is significant (> 0.1s) ======
    if (remainingDuration > 0.1) {
      console.log(`📺 Batch ${batchIndex + 1} (LAST): Adding remaining video: ${remainingStart.toFixed(2)}s to ${totalVideoDuration.toFixed(2)}s (${remainingDuration.toFixed(2)}s)`);
    } else {
      console.log(`📺 Batch ${batchIndex + 1} (LAST): Skipping remaining segment (too short: ${remainingDuration.toFixed(3)}s)`);
    }
  }

  if (isLastBatch && batchEndTime < totalVideoDuration && (totalVideoDuration - batchEndTime) > 0.1) {
    const remainingStart = batchEndTime;
    const remainingDuration = totalVideoDuration - batchEndTime;

    // Add remaining video segment
    const remainingIndex = batch.length;
    let remainingVideoFilter = `[0:v]trim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(6)},setpts=PTS-STARTPTS`;

    if (options.output?.quality) {
      const resolution = getResolution(options);
      remainingVideoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    remainingVideoFilter += `[v${remainingIndex}]`;
    segmentFilters.push(remainingVideoFilter);
    videoSegments.push(`[v${remainingIndex}]`);

    // Add audio for remaining video if needed
    if (holdOriginalAudio) {
      let remainingAudioFilter = `[0:a]atrim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(6)},asetpts=PTS-STARTPTS`;
      remainingAudioFilter += `,highpass=f=80,lowpass=f=8000`;
      // ====== FIX: No delay for remaining segment - it's already positioned correctly ======
      remainingAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[oa${remainingIndex}]`;
      segmentFilters.push(remainingAudioFilter);
      originalAudioSegments.push(`[oa${remainingIndex}]`);
    }

    if (addVoiceAudio) {
      // Generate high-quality silence for remaining duration
      let silentAudioFilter = `aevalsrc=0:duration=${remainingDuration.toFixed(6)}:sample_rate=44100:channel_layout=stereo`;
      // ====== FIX: No delay for remaining segment silence ======
      silentAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[va${remainingIndex}]`;
      segmentFilters.push(silentAudioFilter);
      voiceAudioSegments.push(`[va${remainingIndex}]`);
    }

    currentTime += remainingDuration;
  }

  // === CONCAT VIDEO ===
  const totalSegments = videoSegments.length;
  const videoConcat = `${videoSegments.join('')}concat=n=${totalSegments}:v=1:a=0[vout]`;
  segmentFilters.push(videoConcat);

  // === IMPROVED AUDIO MIXING ===
  let audioMix = '';
  if (holdOriginalAudio && addVoiceAudio) {
    // Mix all audio segments (including initial and remaining if present)
    const allAudioTags = [];
    const weights = [];

    // Handle initial segment if present
    if (originalAudioSegments.includes('[oa_initial]') && voiceAudioSegments.includes('[va_initial]')) {
      allAudioTags.push('[oa_initial][va_initial]');
      weights.push('0.2 1.0'); // Voice dominant since it's heavily normalized
    }

    // Handle regular segments
    for (let i = 0; i < batch.length; i++) {
      if (originalAudioSegments.includes(`[oa${i}]`) && voiceAudioSegments.includes(`[va${i}]`)) {
        allAudioTags.push(`[oa${i}][va${i}]`);
        weights.push('0.2 1.0'); // Voice dominant since it's heavily normalized
      }
    }

    // Handle remaining segment if present
    const remainingIndex = batch.length;
    if (originalAudioSegments.includes(`[oa${remainingIndex}]`) && voiceAudioSegments.includes(`[va${remainingIndex}]`)) {
      allAudioTags.push(`[oa${remainingIndex}][va${remainingIndex}]`);
      weights.push('0.2 1.0'); // Voice dominant since it's heavily normalized
    }

    if (allAudioTags.length > 0) {
      // Enhanced mixing with sidechaining effect (ducking original audio when voice is present)
      audioMix = `${allAudioTags.join('')}amix=inputs=${allAudioTags.length * 2}:duration=longest:weights=${weights.join(' ')},`;

      // Ultra-aggressive final normalization for maximum consistency
      audioMix += `acompressor=threshold=-20dB:ratio=6:attack=2:release=20,`; // Heavy final compression
      audioMix += `loudnorm=I=-16:TP=-1.5:LRA=7,`; // Tight loudness range
      audioMix += `dynaudnorm=f=500:g=25:p=0.85,`; // Very aggressive dynamic normalization
      audioMix += `equalizer=f=1000:width_type=h:width=2:g=-1,`; // Slight mid cut for clarity
      audioMix += `highpass=f=80,lowpass=f=12000[aout]`; // Clean frequency range
    }

  } else if (holdOriginalAudio) {
    const originalAudioTags = originalAudioSegments.join('');
    audioMix = `${originalAudioTags}amix=inputs=${originalAudioSegments.length}:duration=longest,loudnorm=I=-16:TP=-1.5:LRA=11[aout]`;

  } else if (addVoiceAudio) {
    const voiceAudioTags = voiceAudioSegments.join('');
    audioMix = `${voiceAudioTags}amix=inputs=${voiceAudioSegments.length}:duration=longest,loudnorm=I=-16:TP=-1.5:LRA=11[aout]`;
  }

  if (audioMix) segmentFilters.push(audioMix);

  // Rest of the code remains the same...
  const filterComplex = segmentFilters.join('; ');
  const batchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);

  const filterFile = path.join(outputDir, `filter_${batchIndex}.txt`);
  fs.writeFileSync(filterFile, filterComplex);

  // === IMPROVED FFMPEG COMMAND ===
  let ffmpegCmd = `${ffmpegManager.ffmpegPath} -v verbose ${inputs.join(' ')} -filter_complex_script "${filterFile}" -map "[vout]"`;
  if (audioMix) {
    // Higher quality audio encoding
    ffmpegCmd += ` -map "[aout]" -c:a aac -profile:a aac_low -ar 44100 -ac 2 -b:a 192k`;
  } else {
    ffmpegCmd += ` -an`;
  }
  // Additional quality settings
  const encoder = await getEncoder();
  // ffmpegCmd += ` -c:v ${encoder} -preset medium -crf 21 -profile:v high -level 4.1 -pix_fmt yuv420p -movflags +faststart -y "${batchOutput}"`;
  ffmpegCmd += ` -c:v ${encoder} -preset medium -crf 21 -movflags +faststart -y "${batchOutput}"`;

  console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${totalBatches}`);
  console.log(`📊 Batch ${batchIndex + 1} expected duration: ${currentTime.toFixed(2)}s`);

  event?.sender?.send('video-task', {
    data: `🧩 Running FFmpeg for batch ${batchIndex + 1}/${totalBatches} (${currentTime.toFixed(2)}s)`,
    code: 0,
  });

  try {
    await execPromise(ffmpegCmd, { cwd: outputDir });
    fs.unlinkSync(filterFile);
    return batchOutput;
  } catch (error) {
    console.error(`❌ Batch ${batchIndex + 1} processing failed:`, error.message);
    throw error;
  }
}

const processVideoSimplified = async (event, videoPath, srtArray, outputDir, finalOutput, options = {}) => {
  const workDirTemp = path.join(outputDir, '_temp');
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });
  const type = 'video-task';
  const totalVideoDuration = options?.videoInfo?.duration || 0;

  // 1. Calculate audio duration for each segment
  for (const srt of srtArray) {
    if (srt.audioUrl && fs.existsSync(srt.audioUrl.replace('file://', ''))) {
      srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
      event?.sender?.send(type, {
        data: `🕐 Audio duration for segment ${srt.index}: ${srt.duration} seconds`,
        code: 0,
      });
    }
  }

  // 2. Calculate video segment duration and speed ratio (assumed handled by buildBatchAndSrt)
  const batches = buildBatchAndSrt(event, srtArray, BATCH_SIZE);
  const batchVideoPaths = [];

  // ====== FIX: Calculate total processed time correctly ======
  let totalProcessedTime = 0;
  if (srtArray.length > 0) {
    const lastSrt = srtArray[srtArray.length - 1];
    totalProcessedTime = lastSrt.startTime + lastSrt.originalVideoSegmentDuration;
  }

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];

    // ====== DEBUG: Log batch timing info ======
    if (batch.length > 0) {
      const firstSrt = batch[0];
      const lastSrt = batch[batch.length - 1];
      console.log(`🔍 Batch ${batchIndex + 1}: ${firstSrt.startTime.toFixed(2)}s - ${(lastSrt.startTime + lastSrt.originalVideoSegmentDuration).toFixed(2)}s`);
    }

    try {
      const batchOutput = await processBatchWithRetry(event, batch, batchIndex, videoPath, outputDir, options, batches.length, totalVideoDuration, totalProcessedTime);
      batchVideoPaths.push(batchOutput);
    } catch (error) {
      console.error(`❌ Failed to process batch ${batchIndex + 1}:`, error.message);
      throw error;
    }
  }

  // 4. Concatenate batches
  const concatListPath = path.join(outputDir, 'concat_list.txt');
  const concatListContent = batchVideoPaths.map((p) => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);
  const concatOutput = path.join(outputDir, 'concatenated.mp4');
  const encoder = await getEncoder();
  const { flipVideo, scaleFactor, volume } = options.output
  const {width,height} = getResolution(options);
  const flipFilter = flipVideo ? "hflip" : "";
  // const scaleFilter = `scale=ceil((iw*${scaleFactor})/2)*2:ceil((ih*${scaleFactor})/2)*2`;
  const zoom = scaleFactor || 1.2;
  const scaledW = Math.ceil(width * zoom);
  const scaledH = Math.ceil(height * zoom);
  const zoomFilter = `scale=${scaledW}:${scaledH},crop=${width}:${height}`;
  // const videoFilters = [flipFilter, zoomFilter].filter(Boolean).join(", ");
  const videoFilters = "";
  const audioFilter = `volume=${volume || 15}`;

  // Ghép filter_complex nếu có
  let filterComplex = "";
  if (videoFilters && audioFilter) {
    filterComplex = `-filter_complex "[0:v]${videoFilters}[v];[0:a]${audioFilter}[a]" -map "[v]" -map "[a]" `;
  } else if (videoFilters) {
    filterComplex = `-filter_complex "[0:v]${videoFilters}[v]" -map "[v]" -map 0:a? `;
  } else if (audioFilter) {
    filterComplex = `-filter_complex "[0:a]${audioFilter}[a]" -map 0:v -map "[a]" `;
  }

  // Bắt đầu lệnh ffmpeg
  let concatCmd = `${ffmpegManager.ffmpegPath} -f concat -safe 0 -i "${concatListPath}" ${filterComplex}`;

  // Add ultra-aggressive audio normalization to eliminate all volume differences
  if (!filterComplex) {
    filterComplex = `-af "acompressor=threshold=-18dB:ratio=8:attack=1:release=10,loudnorm=I=-16:TP=-1.5:LRA=5,dynaudnorm=f=500:g=30:p=0.8" `;
  } else if (filterComplex.includes('[a]')) {
    // If there's already audio processing, add normalization to it
    filterComplex = filterComplex.replace('[a]" -map "[a]"', '[a_temp]" -af "acompressor=threshold=-18dB:ratio=8:attack=1:release=10,loudnorm=I=-16:TP=-1.5:LRA=5,dynaudnorm=f=500:g=30:p=0.8" -map "[a_temp]"');
  }

  // Chọn encoder
  if (encoder.includes('qsv')) {
    concatCmd += `-c:v ${encoder} -preset fast -global_quality 23 -c:a aac -b:a 192k -y "${concatOutput}"`;
  } else if (encoder.includes('nvenc')) {
    concatCmd += `-c:v ${encoder} -preset fast -cq 23 -c:a aac -b:a 192k -y "${concatOutput}"`;
  } else {
    concatCmd += `-c:v ${encoder} -preset fast -crf 23 -c:a aac -b:a 192k -y "${concatOutput}"`;
  }


  console.log('📦 Running final concat with audio normalization...');
  event?.sender?.send(type, {
    data: '📦 Running final concat with audio normalization...',
    code: 0,
  });
  await execPromise(concatCmd, { cwd: outputDir });

  // backgroundMusic
  const backgroundMusic = options.audio?.backgroundMusic;
  if (backgroundMusic?.enabled && backgroundMusic?.file) {
    const musicPath = options.audio?.backgroundMusic?.file;
    const musicVolume = options.audio?.backgroundMusic?.volume;
    const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    await addAudioToVideo(event, concatOutput, musicPath, outputWithMusic, {
      audioBitrate: '192k',
      volume: musicVolume,
    });
    fs.renameSync(outputWithMusic, concatOutput);
  } else {
    // up volume 1.5x
    // const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    // await upVolume(event, concatOutput, outputWithMusic, 10);
    // fs.renameSync(outputWithMusic, concatOutput);
  }

  // 5. Apply subtitles if needed
  let adjustedSrtPath = 'subtitles.ass'
  if (options.textSubtitle?.enabled) {
    const textSubtitle = options.textSubtitle;

    // ====== FIX: Điều chỉnh subtitle timing nếu có initial segment ======
    const firstSrt = srtArray[0];
    const hasInitialSegment = firstSrt && firstSrt.startTime > 0.1;
    const initialOffset = hasInitialSegment ? firstSrt.startTime : 0;

    // Tạo bản copy của srtArray với timing đã điều chỉnh cho subtitle
    const adjustedSrtArray = srtArray.map(srt => ({
      ...srt,
      adjustedStartTime: (srt.adjustedStartTime || 0) + initialOffset,
      adjustedEndTime: (srt.adjustedEndTime || 0) + initialOffset
    }));

    // Validate custom font name for ASS subtitle
    const fontName = validateFontName(textSubtitle.fontFamily || 'Arial');

    const subtitleOptions = {
      fontSize: textSubtitle.fontSize || 48,
      fontName: fontName,
      // textColor: cssToASSColor(textSubtitle.color || '#000000'),
      textColor: textSubtitle.assColors?.text || '&H000000',
      backgroundColor: textSubtitle.assColors?.background || '&H000000',
      outlineColor: textSubtitle.assColors?.border || '&H000000',
      // backgroundColor: cssToASSColor(textSubtitle.backgroundColor || '#fff700', '00'),
      borderStyle: 4,
      bold: textSubtitle.bold || true,
      addPadding: true,
      alignment: 2,
      marginVertical: 50,
      resolution: options.output.resolution,
      assOptions: textSubtitle.assOptions
    };
    console.log('subtitleOptions', subtitleOptions);
    console.log('textSubtitle', textSubtitle);
    console.log(`📝 Subtitle timing adjustment: initialOffset=${initialOffset.toFixed(2)}s`);

    const assContent = generateASSSubtitle(adjustedSrtArray, subtitleOptions);
    const filePath = path.join(outputDir, adjustedSrtPath)
    fs.writeFileSync(filePath, assContent, 'utf8');
    const tempFontsDir = path.join(outputDir, 'fonts');
    // Copy fonts to temp directory and create fontconfig
    if (fs.existsSync(tempFontsDir)) {
      adjustedSrtPath += `:fontsdir=./fonts/`
    }

    const encoder = await getEncoder();
    const command = `${ffmpegManager.ffmpegPath} -i "${concatOutput}" -vf "ass=${adjustedSrtPath}" -c:a copy -c:v ${encoder} -preset fast -crf 23 -y "${finalOutput}"`;
    await execPromise(command, { cwd: outputDir });
    console.log(`✅ Subtitle applied: ${finalOutput}`);
    event?.sender?.send(type, {
      data: `✅ Subtitle applied: ${finalOutput}`,
      code: 0,
    });
  } else {
    fs.renameSync(concatOutput, finalOutput);
  }

  // 6. Cleanup
  // batchVideoPaths.forEach((p) => {
  //   if (fs.existsSync(p)) fs.unlinkSync(p);
  // });
  // if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);
  // if (fs.existsSync(concatOutput)) fs.unlinkSync(concatOutput);
  // if (fs.existsSync(adjustedSrtPath)) fs.unlinkSync(adjustedSrtPath);

  console.log(`✅ Final video generated: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Final video generated: ${finalOutput}`,
    code: 0,
    success: true,
  });

  return {
    videoPath: finalOutput,
    adjustedSrtArray: srtArray,
  };
};

module.exports = {
  processVideoSimplified,
  processBatchWithRetry,
  buildBatchAndSrt,
  getResolution,
  processSingleBatch
};
